<?php

namespace App\Http\Controllers;

use App\Events\ScriptStatusChanged;
use App\Http\Controllers\Controller;
use App\Models\Import;
use App\Models\ImportFile;
use App\Models\State;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;
use App\Traits\FaxManager;


class ExcelImportController extends Controller
{
    use FaxManager;


    public function newImport()
    {
        $page_title = 'Add New';
        // $has_back = request('return_url') ?: route('scripts.ready-to-sign');

        $livewire_component = 'new-import';
        $livewire_data = [
            'page_title' => $page_title,
            'importFile' => new ImportFile()
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
            // 'has_back' => $has_back
        ]);
    }

    /**
     * Display the PDF with action buttons
     */
    public function viewPdf($id)
    {
        $prescription = ImportFile::findOrFail($id);

        // Check if the file exists
        if (!Storage::exists($prescription->file_path)) {
            session()->flash('error-message', 'PDF file not found.');
            return redirect()->route('dashboard');
        }

        return view('archive.pdf-view', [
            'prescription' => $prescription
        ]);
    }


    public function import()
    {
        $page_title = 'Bulk Import';
        // $has_back = request('return_url') ?: route('imports.index');

        return view('excel-import.form', [
            'page_title' => $page_title,
            // 'has_back' => $has_back
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'excel_file' => 'required|file|mimes:xlsx,xls',
        ]);

        // Get the uploaded file
        $file = $request->file('excel_file');

        // Store the file temporarily
        $tempPath = $file->store('temp', 'local');

        // Store the file path in session for later use
        session(['temp_excel_file' => $tempPath]);
        session(['original_file_name' => $file->getClientOriginalName()]);

        // Read the Excel file directly without saving it
        // $spreadsheet = IOFactory::load(storage_path('app/public/' . $tempPath));
        $spreadsheet = IOFactory::load(storage_path('app/' . $tempPath));

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        // Get the highest row and column index
        $highestRow = $sheet->getHighestDataRow();
        $highestColumn = $sheet->getHighestDataColumn();
        $highestColumnIndex = Coordinate::columnIndexFromString($highestColumn);

        // Initialize variable to track cell errors
        $cellErrors = [];

        // Read column headers from row 1 (assuming headers are in row 1)
        $headerRow = 1;
        $columnHeaders = [];
        $sigColumnIndex = -1; // Track the index of the SIG column if found
        $noteColumnIndex = -1; // Track the index of the Note column if found

        // Define base expected headers
        $expectedHeaders = [
            'Script Date',
            'Last Name',
            'First Name',
            'DOB',
            'Gender',
            'Address',
            'City',
            'State',
            'Zipcode',
            'Phone',
            'Medication',
            'Stregnth', // Fixed typo from 'Stregnth' to 'Stregnth'
            'Dosing',
            'Refills',
            'Vial Qty',
            'SIG',
            'Note'
        ];



        // Initialize empty array for column order errors (we're not validating column names anymore)
        $columnOrderErrors = [];

        // Read only the first 17 column headers
        $maxColumns = min(17, $highestColumnIndex);
        for ($col = 1; $col <= $maxColumns; $col++) {
            $cellCoord = Coordinate::stringFromColumnIndex($col) . $headerRow;
            $headerValue = $sheet->getCell($cellCoord)->getValue();
            $headerValue = $headerValue ?: 'Column ' . $col; // Use generic name if header is empty
            $columnHeaders[] = $headerValue;
        }



        // We're no longer validating column order/names - all column names are accepted

        // Start from row 2 (after the header row)
        $startRow = 2;
        $allRowsData = [];
        $errorRows = [];

        // Process each row after the header row
        $consecutiveBlankRows = 0; // Track consecutive blank rows
        $blankRowsBuffer = []; // Buffer to store blank rows temporarily
        $blankRowsErrorsBuffer = []; // Buffer to store blank row errors temporarily
        $blankRowsIndices = []; // Buffer to store blank row indices temporarily

        for ($row = $startRow; $row <= $highestRow; $row++) {
            $rowData = [];
            $hasData = false;
            $rowCellErrors = []; // Track errors for this specific row

            // Extract data from each cell in the current row (only first 17 columns)
            $maxColumns = min(17, $highestColumnIndex);
            for ($col = 1; $col <= $maxColumns; $col++) {
                $cellCoord = Coordinate::stringFromColumnIndex($col) . $row;
                $cellValue = $sheet->getCell($cellCoord)->getValue();

                // Preserve the exact structure of the Excel file, including blank cells
                $rowData[] = $cellValue;

                // Check if this cell has any data
                if ($cellValue !== null && $cellValue !== '') {
                    $hasData = true;
                }
            }

            // Check if the row is completely blank
            if (!$hasData) {
                // Increment consecutive blank rows counter
                $consecutiveBlankRows++;

                // Add a special error message for blank rows
                $rowCellErrors[0] = 'Blank row';

                // Instead of adding directly to allRowsData, store in buffer
                $blankRowsBuffer[] = $rowData;
                $blankRowsErrorsBuffer[$row] = $rowCellErrors;
                $blankRowsIndices[] = $row;

                Log::info('Blank row detected', [
                    'row_number' => $row,
                    'consecutive_blank_rows' => $consecutiveBlankRows
                ]);

                // If we've found more than 5 consecutive blank rows, stop processing
                // and discard all buffered blank rows
                if ($consecutiveBlankRows >= 5) {
                    Log::info('5 or more consecutive blank rows detected - stopping processing and discarding blank rows', [
                        'row_number' => $row,
                        'consecutive_blank_rows' => $consecutiveBlankRows
                    ]);
                    // Clear the buffers - we don't want to add these blank rows
                    $blankRowsBuffer = [];
                    $blankRowsErrorsBuffer = [];
                    $blankRowsIndices = [];
                    break; // Exit the loop
                }
            } else {
                // We found a non-blank row

                // If we had some blank rows but less than 5 consecutive ones,
                // add them from the buffer to our main arrays
                if ($consecutiveBlankRows > 0 && $consecutiveBlankRows < 5) {
                    foreach ($blankRowsBuffer as $bufferRowData) {
                        $allRowsData[] = $bufferRowData;
                    }

                    foreach ($blankRowsErrorsBuffer as $bufferRowIndex => $bufferRowErrors) {
                        $cellErrors[$bufferRowIndex] = $bufferRowErrors;
                    }

                    foreach ($blankRowsIndices as $bufferRowIndex) {
                        $errorRows[] = $bufferRowIndex;
                    }
                }

                // Reset buffers and counter
                $blankRowsBuffer = [];
                $blankRowsErrorsBuffer = [];
                $blankRowsIndices = [];
                $consecutiveBlankRows = 0;

                // Validate non-blank rows
                list($isValid, $rowCellErrors) = $this->validateRowData($rowData);
                if (!$isValid) {
                    $errorRows[] = $row;
                }

                // Store cell errors for this row
                if (!empty($rowCellErrors)) {
                    $cellErrors[$row] = $rowCellErrors;
                }

                // Add the non-blank row data directly to our collection
                $allRowsData[] = $rowData;
            }

            // Log the row structure for debugging
            Log::info('Processing row', [
                'row_number' => $row,
                'column_count' => count($rowData),
                'has_data' => $hasData,
                'highest_column_index' => $highestColumnIndex,
                'consecutive_blank_rows' => $consecutiveBlankRows
            ]);
        }

        // Handle case where file ends with less than 5 blank rows
        // If we had some blank rows but less than 5 consecutive ones at the end of the file,
        // add them from the buffer to our main arrays
        if ($consecutiveBlankRows > 0 && $consecutiveBlankRows < 5) {
            foreach ($blankRowsBuffer as $bufferRowData) {
                $allRowsData[] = $bufferRowData;
            }

            foreach ($blankRowsErrorsBuffer as $bufferRowIndex => $bufferRowErrors) {
                $cellErrors[$bufferRowIndex] = $bufferRowErrors;
            }

            foreach ($blankRowsIndices as $bufferRowIndex) {
                $errorRows[] = $bufferRowIndex;
            }
        }

        // If no data was found, return with an error
        if (empty($allRowsData)) {
            return back()->with('error', 'No data was found beyond row 2 in the Excel file.');
        }

        // Store row data in session for the preview and final processing
        session(['all_prescription_data' => $allRowsData]);
        session(['error_rows' => $errorRows]);
        session(['cell_errors' => $cellErrors]);
        session(['column_order_errors' => $columnOrderErrors]);
        session(['column_headers' => $columnHeaders]);
        session(['sig_column_index' => $sigColumnIndex]);

        // Calculate statistics
        $totalRows = count($allRowsData);

        // Count rows with errors (including blank rows)
        $errorCount = count($errorRows);

        // Calculate valid rows
        $validRows = $totalRows - $errorCount;

        // Log error statistics
        Log::info('Error statistics', [
            'total_rows' => $totalRows,
            'error_rows' => $errorCount,
            'valid_rows' => $validRows
        ]);

        $has_back = request('return_url') ?: route('excel.import');
        $page_title = 'Data Preview';

        // Return the preview view
        return view('excel-import.preview', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'allRowsData' => $allRowsData,
            'errorRows' => $errorRows,
            'cellErrors' => $cellErrors,
            'columnOrderErrors' => $columnOrderErrors,
            'totalRows' => $totalRows,
            'errorCount' => $errorCount,
            'validRows' => $validRows,
            'columnHeaders' => $columnHeaders,
            'expectedHeaders' => $expectedHeaders,
            'currentStep' => 2,
            'totalSteps' => 4
        ]);
    }

    /**
     * Process the data after preview and generate PDFs
     */
    public function process()
    {
        // Get the data from session
        $allRowsData = session('all_prescription_data', []);
        $errorRows = session('error_rows', []);
        $tempFilePath = session('temp_excel_file');
        $originalFileName = session('original_file_name');

        if (empty($allRowsData) || !$tempFilePath) {
            return redirect()->route('excel.import')->with('error', 'Please upload an Excel file to continue.');
        }

        // Log error rows for debugging
        Log::info('Processing Excel data', [
            'total_rows' => count($allRowsData),
            'error_rows' => $errorRows,
            'error_rows_count' => count($errorRows)
        ]);

        // Double-check that we're only processing valid rows
        if (count($errorRows) > 0) {
            Log::info('Filtering out error rows before processing');
        }

        // Create a new entry in the Import model with the logged-in user's ID
        $import = Import::create([
            'file_name' => $originalFileName,
            'user_id' => auth()->id()
        ]);

        // Create directory for storing PDFs
        $storagePath = 'public/prescriptions/' . $import->id;
        Storage::makeDirectory($storagePath);

        // Track processed rows for statistics
        $processedRowCount = 0;
        $skippedRowCount = 0;

        // Generate and save PDFs for each valid row
        // Note: We don't need to handle consecutive blank rows here since they were already
        // filtered out in the store method. The allRowsData array should not contain
        // 5 or more consecutive blank rows.
        foreach ($allRowsData as $index => $rowData) {
            // Calculate the actual Excel row number (Excel row number starts from 2)
            $excelRowNumber = $index + 2;

            // Check if this row has any data
            $hasData = false;
            foreach ($rowData as $cellValue) {
                if ($cellValue !== null && $cellValue !== '') {
                    $hasData = true;
                    break;
                }
            }

            // Skip completely empty rows
            if (!$hasData) {
                Log::info('Skipping empty row', [
                    'excel_row_number' => $excelRowNumber,
                    'index' => $index
                ]);
                continue;
            }

            // Skip rows with errors - check if this row's Excel row number is in the error rows array
            if (in_array($excelRowNumber, $errorRows)) {
                Log::info('Skipping row with errors', [
                    'excel_row_number' => $excelRowNumber,
                    'index' => $index
                ]);
                $skippedRowCount++;
                continue;
            }



            // Additional check to ensure all required fields are present
            $hasAllRequiredFields = true;
            for ($i = 0; $i <= 15; $i++) { // Check all required fields (0-15)
                // Skip Phone (index 9) and Sig (index 15) as they are optional
                if ($i === 9 || $i === 15) {
                    continue;
                }

                if (!isset($rowData[$i]) || $rowData[$i] === null || $rowData[$i] === '') {
                    $hasAllRequiredFields = false;
                    Log::info('Skipping row with missing required field', [
                        'excel_row_number' => $excelRowNumber,
                        'index' => $index,
                        'missing_field_index' => $i
                    ]);
                    break;
                }
            }

            if (!$hasAllRequiredFields) {
                $skippedRowCount++;
                continue;
            }

            // Get the logged-in user
            $user = Auth::user();
            $userState = null;
            $doctorName = 'Dr. April'; // Default name

            if ($user) {
                // Get the user's full name for the signature
                $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);

                // Get the user's state information if available
                if ($user->state_id) {
                    $userState = State::find($user->state_id);
                }
            }

            // Get default indices for data fields
            $adjustedIndices = $this->getAdjustedIndices(-1);

            // Generate PDF
            $pdf = PDF::loadView('pdf.prescription', [
                'data' => $rowData,
                'isPdfDownload' => true,
                'user' => $user,
                'userState' => $userState,
                'doctorName' => $doctorName,
                'isSigned' => false,
                'adjustedIndices' => $adjustedIndices
            ]);
            $pdf->setPaper('letter');

            // Format patient name for filename
            $patientName = isset($rowData[2]) && isset($rowData[1]) ?
                str_replace(' ', '_', $rowData[2] . '_' . $rowData[1]) : 'patient';
            $fileName = 'prescription_' . ($index + 1) . '_' . $patientName . '.pdf';

            // Save PDF to storage
            $filePath = $storagePath . '/' . $fileName;
            Storage::put($filePath, $pdf->output());

            // Get default indices for data fields
            $adjustedIndices = $this->getAdjustedIndices(-1);

            // Convert Excel dates to MySQL date format
            $scriptDate = $this->convertExcelDate($rowData[$adjustedIndices['script_date']]);
            $dob = $this->convertExcelDate($rowData[$adjustedIndices['dob']]);

            // Use the Excel row number for the 'number' field
            $rowNumber = $excelRowNumber;

            // Create ImportFile record
            ImportFile::create([
                'import_id' => $import->id,
                'file_name' => $fileName,
                'file_path' => $filePath,
                'script_date' => $scriptDate,
                'last_name' => $rowData[$adjustedIndices['last_name']],
                'first_name' => $rowData[$adjustedIndices['first_name']],
                'dob' => $dob,
                'gender' => $rowData[$adjustedIndices['gender']],
                'address' => $rowData[$adjustedIndices['address']],
                'city' => $rowData[$adjustedIndices['city']],
                'state' => $rowData[$adjustedIndices['state']],
                'zip' => $rowData[$adjustedIndices['zip']],
                'phone' => isset($rowData[$adjustedIndices['phone']]) && $rowData[$adjustedIndices['phone']] !== '' ? $rowData[$adjustedIndices['phone']] : null,
                'medication' => $rowData[$adjustedIndices['medication']],
                'stregnth' => $rowData[$adjustedIndices['stregnth']],
                'dosing' => $rowData[$adjustedIndices['dosing']],
                'sig' => isset($rowData[$adjustedIndices['sig']]) && $rowData[$adjustedIndices['sig']] !== '' ? $rowData[$adjustedIndices['sig']] : '',
                'notes' => isset($rowData[$adjustedIndices['notes']]) && $rowData[$adjustedIndices['notes']] !== '' ? $rowData[$adjustedIndices['notes']] : '',
                'refills' => $rowData[$adjustedIndices['refills']],
                'vial_quantity' => $rowData[$adjustedIndices['vial_quantity']],
                'number' => $rowNumber,
            ]);

            $processedRowCount++;
        }

        // Log processing results
        Log::info('Excel processing completed', [
            'processed_rows' => $processedRowCount,
            'skipped_rows' => $skippedRowCount,
            'import_id' => $import->id
        ]);

        // Clean up the temporary file
        if ($tempFilePath) {
            Storage::delete($tempFilePath);
        }

        // Clear session data
        session()->forget(['temp_excel_file', 'original_file_name']);

        $has_back = request('return_url') ?: route('excel.import');

        $page_title = 'Prescriptions List';

        // Return a view that lists all prescriptions
        return view('pdf.prescription-list', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'import' => $import,
            'import_id' => $import->id,
            'currentStep' => 3,
            'totalSteps' => 4,
            'processedRowCount' => $processedRowCount,
            'skippedRowCount' => $skippedRowCount
        ]);
    }

    public function view(Request $request)
    {
        $page_title = 'Prescriptions List';
        $has_back = request('return_url') ?: route('imports.index');

        // Count scripts
        $scriptsCount = 0;
        if ($request->id) {
            $scriptsCount = ImportFile::where('import_id', $request->id)->count();
        }

        // Return a view that lists all prescriptions
        return view('pdf.prescription-list', [
            'page_title' => $page_title,
            'import_id' => $request->id,
            'has_back' => $has_back,
            'scriptsCount' => $scriptsCount,
            'statusText' => ImportFile::STATUS_NEW,
            'currentStep' => 3,
            'totalSteps' => 4
        ]);
    }

    /**
     * View signed prescriptions - Redirects to view method since we no longer have a sign step
     */
    public function viewSigned(Request $request)
    {
        // Redirect to the view method since we no longer have a sign step
        return redirect()->route('excel.view', ['id' => $request->importId]);
    }

    /**
     * View pending approval prescriptions
     */
    public function viewSent(Request $request)
    {
        $page_title = 'Pending Approval Prescriptions';
        $has_back = request('return_url') ?: route('excel.import');

        // Count pending approval scripts
        $scriptsCount = 0;
        if ($request->importId) {
            $scriptsCount = ImportFile::where('import_id', $request->importId)
                ->where('status', ImportFile::STATUS_PENDING_APPROVAL)
                ->count();
        }

        // Return a view that lists pending approval prescriptions
        return view('pdf.prescription-list', [
            'page_title' => $page_title,
            'import_id' => $request->importId,
            'has_back' => $has_back,
            'scriptsCount' => $scriptsCount,
            'statusText' => 'pending approval',
            'currentStep' => 4,
            'totalSteps' => 4
        ]);
    }

    /**
     * Generate and download a PDF prescription
     *
     * @param int $id The ID of the ImportFile to download
     * @return \Illuminate\Http\Response
     */
    public function downloadPdf($id = null)
    {
        // If ID is provided, get the specific ImportFile
        if ($id !== null) {
            $importFile = ImportFile::find($id);
            if ($importFile) {
                // Check if file exists in storage
                if (Storage::exists($importFile->file_path)) {
                    // Create a more descriptive filename based on patient data
                    $fileName = 'prescription_' . $importFile->id . '_' .
                        $importFile->last_name . '_' .
                        $importFile->first_name . '.pdf';

                    // Replace spaces with underscores and remove any special characters
                    $fileName = str_replace(' ', '_', $fileName);
                    $fileName = preg_replace('/[^A-Za-z0-9\._-]/', '', $fileName);

                    return Storage::download($importFile->file_path, $fileName);
                }
            }
        }

        // If no valid ID or file not found, try to get the first ImportFile from the latest Import
        $latestImport = Import::latest()->first();
        if ($latestImport) {
            $firstFile = $latestImport->files()->first();
            if ($firstFile && Storage::exists($firstFile->file_path)) {
                // Create a more descriptive filename based on patient data
                $fileName = 'prescription_' . $firstFile->id . '_' .
                    $firstFile->last_name . '_' .
                    $firstFile->first_name . '.pdf';

                // Replace spaces with underscores and remove any special characters
                $fileName = str_replace(' ', '_', $fileName);
                $fileName = preg_replace('/[^A-Za-z0-9\._-]/', '', $fileName);

                return Storage::download($firstFile->file_path, $fileName);
            }
        }

        // If all else fails, generate a PDF from session data (fallback)
        $allData = session('all_prescription_data', []);
        if (!empty($allData)) {
            $data = $allData[0] ?? [];
            $fileName = 'prescription.pdf';

            // Get the logged-in user
            $user = Auth::user();
            $userState = null;
            $doctorName = 'Dr. April'; // Default name

            if ($user) {
                // Get the user's full name for the signature
                $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);

                // Get the user's state information if available
                if ($user->state_id) {
                    $userState = State::find($user->state_id);
                }
            }

            // Get default indices for data fields
            $adjustedIndices = $this->getAdjustedIndices(-1);

            // Generate PDF
            $pdf = PDF::loadView('pdf.prescription', [
                'data' => $data,
                'isPdfDownload' => true,
                'user' => $user,
                'userState' => $userState,
                'doctorName' => $doctorName,
                'isSigned' => false,
                'adjustedIndices' => $adjustedIndices
            ]);
            $pdf->setPaper('letter');

            return $pdf->download($fileName);
        }

        // No data available
        return back()->with('error', 'No script found.');
    }

    /**
     * Download all prescriptions as a ZIP file
     *
     * @param int $importId The ID of the Import to download all files for
     * @return \Illuminate\Http\Response
     */
    public function downloadAllPdf($importId = null)
    {
        // Get the Import record
        $import = $importId ? Import::find($importId) : Import::latest()->first();

        if (!$import) {
            return back()->with('error', 'Import record could not be found.');
        }

        // Determine the current status based on the request or route
        $status = request()->input('status');
        $currentRoute = request()->route()->getName();

        // If status is not provided in the request, determine it from the route
        if (!$status) {
            if ($currentRoute === 'excel.view-sent') {
                $status = ImportFile::STATUS_PENDING_APPROVAL;
            } else {
                // Default to showing 'New' status on the main view
                $status = ImportFile::STATUS_NEW;
            }
        }

        // Map text status to actual status values
        if ($status === 'signed') {
            $status = ImportFile::STATUS_SIGNED;
        } elseif ($status === 'pending approval') {
            $status = ImportFile::STATUS_PENDING_APPROVAL;
        } elseif ($status === 'created') {
            $status = ImportFile::STATUS_NEW;
        }

        // Get ImportFile records for this Import filtered by status
        $query = ImportFile::where('import_id', $import->id);

        if ($status) {
            if ($status === ImportFile::STATUS_NEW) {
                $query->where(function ($q) {
                    $q->where('status', ImportFile::STATUS_NEW)
                        ->orWhereNull('status');
                });
            } else {
                $query->where('status', $status);
            }
        }

        // Get the IDs of records currently displayed in the table
        // This ensures we download exactly what's shown in the UI
        $displayedIds = request()->input('displayed_ids');
        $allWithStatus = request()->input('all_with_status');

        // Log the request details for debugging
        Log::info('Download All PDF Request', [
            'displayed_ids' => $displayedIds,
            'all_with_status' => $allWithStatus,
            'import_id' => $importId,
            'status' => $status,
            'route' => $currentRoute,
            'request_method' => request()->method(),
            'all_request_params' => request()->all()
        ]);

        // Get the current query SQL for debugging
        $initialSql = $query->toSql();
        $initialBindings = $query->getBindings();
        Log::info('Initial query before filtering by IDs', [
            'sql' => $initialSql,
            'bindings' => $initialBindings
        ]);

        // Count how many records would be returned without ID filtering
        $countBeforeIdFiltering = (clone $query)->count();
        Log::info('Record count before ID filtering', [
            'count' => $countBeforeIdFiltering
        ]);

        // If we have specific IDs, use them
        if ($displayedIds && is_array($displayedIds) && !empty($displayedIds)) {
            // Convert string IDs to integers if needed
            $numericIds = array_map(function ($id) {
                return is_numeric($id) ? (int)$id : $id;
            }, $displayedIds);

            // Only include the specific IDs provided
            $query->whereIn('id', $numericIds);

            Log::info('Filtering by displayed IDs', [
                'count' => count($numericIds),
                'ids' => $numericIds
            ]);

            // Get the updated query SQL for debugging
            $filteredSql = $query->toSql();
            $filteredBindings = $query->getBindings();
            Log::info('Query after filtering by IDs', [
                'sql' => $filteredSql,
                'bindings' => $filteredBindings
            ]);
        }
        // If all_with_status is true, we don't need to filter by IDs
        else if ($allWithStatus == 'true') {
            Log::info('Using all records with status: ' . $status);
            // No additional filtering needed, we'll use all records with the current status
        }
        // No IDs and no all_with_status flag
        else {
            Log::info('No displayed IDs provided and no all_with_status flag, using status filtering only');

            // If there are no records with this status, we'll return an error
            if ($countBeforeIdFiltering == 0) {
                Log::warning('No records found with status: ' . $status);
                return back()->with('error', 'No scripts found for this import with the selected status.');
            }
        }

        // Order by ID to ensure consistent ordering
        $query->orderBy('id', 'desc');

        $importFiles = $query->get();

        // Log the final results
        Log::info('Files that will be included in the ZIP', [
            'count' => $importFiles->count(),
            'file_ids' => $importFiles->pluck('id')->toArray(),
            'file_names' => $importFiles->pluck('file_name')->toArray()
        ]);

        // Check if we have any files to process
        if ($importFiles->isEmpty()) {
            Log::warning('No files found matching the criteria', [
                'import_id' => $importId,
                'status' => $status,
                'displayed_ids' => $displayedIds,
                'all_with_status' => $allWithStatus
            ]);

            // If we're using all_with_status and still got no results, there's a real problem
            if ($allWithStatus == 'true') {
                return back()->with('error', 'No script found with the selected status: ' . $status);
            }

            // If we're using displayed_ids but they didn't match any records, try again with all_with_status
            if ($displayedIds && is_array($displayedIds) && !empty($displayedIds)) {
                Log::info('Displayed IDs did not match any records, trying again with all records of this status');

                // Create a new query without ID filtering
                $newQuery = ImportFile::where('import_id', $import->id);

                if ($status) {
                    if ($status === ImportFile::STATUS_NEW) {
                        $newQuery->where(function ($q) {
                            $q->where('status', ImportFile::STATUS_NEW)
                                ->orWhereNull('status');
                        });
                    } else {
                        $newQuery->where('status', $status);
                    }
                }

                $newQuery->orderBy('id', 'desc');
                $importFiles = $newQuery->get();

                // If we still have no files, return an error
                if ($importFiles->isEmpty()) {
                    return back()->with('error', 'No scripts found for this import with the selected status.');
                }

                Log::info('Found ' . $importFiles->count() . ' files after removing ID filtering');
            } else {
                // No displayed_ids and no files found
                return back()->with('error', 'No scripts found for this import with the selected status.');
            }
        }

        // Create a temporary directory to store PDFs
        $tempDir = storage_path('app/temp/prescriptions_' . time());
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        // Copy each file to the temp directory
        foreach ($importFiles as $importFile) {
            if (Storage::exists($importFile->file_path)) {
                $fileContent = Storage::get($importFile->file_path);

                // Create a more descriptive filename based on patient data
                // Include the number field to make the filename more meaningful to the user
                $fileName = 'prescription_' .
                    ($importFile->number ? 'row' . $importFile->number . '_' : '') .
                    $importFile->id . '_' .
                    $importFile->last_name . '_' .
                    $importFile->first_name . '.pdf';

                // Replace spaces with underscores and remove any special characters
                $fileName = str_replace(' ', '_', $fileName);
                $fileName = preg_replace('/[^A-Za-z0-9\._-]/', '', $fileName);

                file_put_contents($tempDir . '/' . $fileName, $fileContent);
            }
        }

        // Create a ZIP file
        $zipFileName = 'prescriptions_' . date('Y-m-d') . '.zip';
        $zipFilePath = storage_path('app/temp/' . $zipFileName);

        $zip = new \ZipArchive();
        if ($zip->open($zipFilePath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === TRUE) {
            // Add files to the zip
            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($tempDir),
                \RecursiveIteratorIterator::LEAVES_ONLY
            );

            foreach ($files as $file) {
                if (!$file->isDir()) {
                    $filePath = $file->getRealPath();
                    // Use a more unique naming scheme to prevent filename collisions
                    // Include the full path hash to ensure uniqueness
                    $pathHash = substr(md5($filePath), 0, 8);
                    $relativePath = $pathHash . '_' . basename($filePath);
                    $zip->addFile($filePath, $relativePath);
                }
            }

            $zip->close();

            // Clean up the temporary directory
            array_map('unlink', glob("$tempDir/*.*"));
            rmdir($tempDir);

            // Return the ZIP file for download
            return response()->download($zipFilePath, $zipFileName, [
                'Content-Type' => 'application/zip'
            ])->deleteFileAfterSend(true);
        }

        return back()->with('error', 'Unable to create ZIP file. Please try again later.');
    }

    /**
     * API endpoint for prescription list datatable
     *
     * @param Request $request
     * @param int|null $importId
     * @return \Illuminate\Http\JsonResponse
     */
    public function prescriptionListApi(Request $request, $importId = null)
    {
        // Get the Import record
        $import = $importId ? Import::find($importId) : Import::latest()->first();

        if (!$import) {
            return response()->json([
                'data' => [],
                'meta' => [
                    'page' => 1,
                    'pages' => 0,
                    'perpage' => 10,
                    'total' => 0,
                ]
            ]);
        }

        // Query ImportFiles for this Import
        $query = ImportFile::where('import_id', $import->id);

        // Create a base query to get the total count of all records (regardless of search/filter)
        $baseQuery = ImportFile::where('import_id', $import->id);

        // Filter by status if provided
        $status = $request->input('status');
        $currentRoute = $request->route()->getName();

        if ($status) {
            $query->where('status', $status);
            $baseQuery->where('status', $status);
            // Removed 'excel.view-signed' case as it's no longer needed
        } else if ($currentRoute === 'excel.view-sent') {
            $query->where('status', ImportFile::STATUS_PENDING_APPROVAL);
            $baseQuery->where('status', ImportFile::STATUS_PENDING_APPROVAL);
        } else {
            // Default to showing 'New' status on the main view
            $query->where(function ($q) {
                $q->where('status', ImportFile::STATUS_NEW)
                    ->orWhereNull('status');
            });
            $baseQuery->where(function ($q) {
                $q->where('status', ImportFile::STATUS_NEW)
                    ->orWhereNull('status');
            });
        }

        // Handle search
        $search = $request->input('query.search', '');
        if ($search) {
            $query_search = "%" . $search . "%";

            // Try to parse the search term as a date
            $dateSearch = null;
            $possibleDateFormats = ['m/d/Y', 'd/m/Y', 'Y-m-d', 'm-d-Y', 'd-m-Y'];

            foreach ($possibleDateFormats as $format) {
                $date = \DateTime::createFromFormat($format, $search);
                if ($date !== false) {
                    $dateSearch = $date->format('Y-m-d');
                    break;
                }
            }

            $query->where(function ($q) use ($query_search, $search, $dateSearch) {
                // If we have a valid date search, use it for script_date
                if ($dateSearch) {
                    $q->where('script_date', 'like', $dateSearch . '%');
                } else {
                    // Otherwise try to match script_date as a string
                    $q->where('script_date', 'like', $query_search);
                }

                // Search in other fields
                $q->orWhere('last_name', 'like', $query_search)
                    ->orWhere('first_name', 'like', $query_search)
                    ->orWhere('medication', 'like', $query_search)
                    ->orWhere('status', 'like', $query_search);
            });
        }

        // Handle sorting
        $sort_order = $request->input('sort.sort', 'desc');
        $sort_field = $request->input('sort.field', 'id');

        if ($sort_order && $sort_field) {
            if (in_array($sort_field, Schema::getColumnListing((new ImportFile())->table))) {
                $query->orderBy($sort_field, $sort_order);
            }
        } else {
            $query->latest();
        }

        // Handle pagination
        $page = $request->input('pagination.page', 1);
        $perpage = $request->input('pagination.perpage', 10);

        // Get the total count of filtered records
        $total = $query->count();

        // Get the total count of all records (regardless of search)
        $totalAll = $baseQuery->count();

        $files = $query->skip(($page - 1) * $perpage)
            ->take($perpage)
            ->get();

        return response()->json([
            'data' => $files,
            'meta' => [
                'page' => (int)$page,
                'pages' => ceil($total / $perpage),
                'perpage' => (int)$perpage,
                'total' => $total,
                'total_all' => $totalAll, // Add the total count of all records
            ]
        ]);
    }

    /**
     * Validate row data from Excel
     *
     * @param array $rowData The row data from Excel
     * @return array [bool $isValid, array $cellErrors]
     */
    private function validateRowData($rowData)
    {
        // Track which cells have errors
        $cellErrors = [];
        $isValid = true;

        // We don't need to store the expected column types here as they're
        // already defined in the validation rules below

        // Validate each column based on specific rules
        // Only validate the first 17 columns (0-16)
        for ($i = 0; $i < min(17, count($rowData)); $i++) {
            $value = $rowData[$i] ?? null;

            // Skip validation if the cell is empty
            if ($value === null || $value === '') {
                // Make Phone (index 9), Sig (index 15), and Note (index 16) optional
                if ($i !== 9 && $i !== 15 && $i !== 16) {
                    // All other fields are required, mark as error
                    $cellErrors[$i] = 'A required value is missing. Please complete all necessary fields.';
                    $isValid = false;
                }
                continue;
            }

            // Validate based on column index
            switch ($i) {
                case 0: // Script Date - must be a valid date in mm/dd/yyyy format
                    if (is_numeric($value)) {
                        try {
                            Date::excelToDateTimeObject($value);
                        } catch (\Exception $e) {
                            $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                            $isValid = false;
                        }
                    } else {
                        // Check if it's in mm/dd/yyyy format
                        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $value, $matches)) {
                            $month = (int)$matches[1];
                            $day = (int)$matches[2];
                            $year = (int)$matches[3];

                            // Validate month and day values
                            if ($month < 1 || $month > 12 || $day < 1 || $day > 31) {
                                $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format';
                                $isValid = false;
                            }
                        }
                        // Check if it's in yyyy/mm/dd format (common error)
                        else if (
                            preg_match('/^(\d{4})\/(\d{1,2})\/(\d{1,2})$/', $value, $matches) ||
                            preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $value, $matches)
                        ) {
                            $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                            $isValid = false;
                        }
                        // Check if it's in dd/mm/yyyy format (another common error)
                        else if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $value, $matches)) {
                            // This could be mm/dd/yyyy or dd/mm/yyyy - if day > 12, it's likely dd/mm/yyyy
                            $first = (int)$matches[1];
                            if ($first > 12) {
                                $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                                $isValid = false;
                            }
                        } else {
                            // Try to parse as a string date
                            $parsedDate = strtotime($value);
                            if ($parsedDate === false) {
                                $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                                $isValid = false;
                            } else {
                                // Even if we can parse it, we want to enforce mm/dd/yyyy format
                                if (!preg_match('/^\d{1,2}\/\d{1,2}\/\d{4}$/', $value)) {
                                    $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                                    $isValid = false;
                                }
                            }
                        }
                    }
                    break;

                case 1: // Last Name - must start with text
                case 2: // First Name - must start with text
                    if (!preg_match('/^[a-zA-Z]/', $value)) {
                        $cellErrors[$i] = 'Entry must begin with a letter.';
                        $isValid = false;
                    }
                    break;

                case 3: // DOB - must be a valid date in mm/dd/yyyy format
                    if (is_numeric($value)) {
                        try {
                            Date::excelToDateTimeObject($value);
                        } catch (\Exception $e) {
                            $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                            $isValid = false;
                        }
                    } else {
                        // Check if it's in mm/dd/yyyy format
                        if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $value, $matches)) {
                            $month = (int)$matches[1];
                            $day = (int)$matches[2];
                            $year = (int)$matches[3];

                            // Validate month and day values
                            if ($month < 1 || $month > 12 || $day < 1 || $day > 31) {
                                $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format';
                                $isValid = false;
                            }
                        }
                        // Check if it's in yyyy/mm/dd format (common error)
                        else if (
                            preg_match('/^(\d{4})\/(\d{1,2})\/(\d{1,2})$/', $value, $matches) ||
                            preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $value, $matches)
                        ) {
                            $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                            $isValid = false;
                        }
                        // Check if it's in dd/mm/yyyy format (another common error)
                        else if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $value, $matches)) {
                            // This could be mm/dd/yyyy or dd/mm/yyyy - if day > 12, it's likely dd/mm/yyyy
                            $first = (int)$matches[1];
                            if ($first > 12) {
                                $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                                $isValid = false;
                            }
                        } else {
                            // Try to parse as a string date
                            $parsedDate = strtotime($value);
                            if ($parsedDate === false) {
                                $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                                $isValid = false;
                            } else {
                                // Even if we can parse it, we want to enforce mm/dd/yyyy format
                                if (!preg_match('/^\d{1,2}\/\d{1,2}\/\d{4}$/', $value)) {
                                    $cellErrors[$i] = 'Invalid date format. Please use the mm/dd/yyyy format.';
                                    $isValid = false;
                                }
                            }
                        }
                    }
                    break;

                case 4: // Gender - must be M or F
                    if (!in_array(strtoupper($value), ['M', 'F'])) {
                        $cellErrors[$i] = "Entry must be 'M' or 'F'.";
                        $isValid = false;
                    }
                    break;

                case 5: // Address - must be text or mix of text and numbers
                    if (is_numeric($value)) {
                        $cellErrors[$i] = 'Entry cannot consist of numbers only.';
                        $isValid = false;
                    }
                    break;

                case 6: // City - must be text or mix of text and numbers
                    if (is_numeric($value)) {
                        $cellErrors[$i] = 'Entry cannot consist of numbers only.';
                        $isValid = false;
                    }
                    break;

                case 7: // State - must be capital letters
                    if (!preg_match('/^[A-Z]+$/', $value)) {
                        $cellErrors[$i] = 'Must be capital letters only';
                        $isValid = false;
                    }
                    break;

                case 8: // Zipcode - must be valid format
                    // US ZIP code formats: 12345 or 12345-6789
                    if (!preg_match('/^\d{5}(-\d{4})?$/', $value)) {
                        $cellErrors[$i] = 'Invalid ZIP code format';
                        $isValid = false;
                    }
                    break;

                case 9: // Phone - validate only if it contains non-numeric characters
                    // Allow empty phone numbers
                    if ($value === null || $value === '') {
                        break;
                    }

                    // Remove common phone formatting characters that are acceptable
                    $cleanedValue = preg_replace('/[\s\(\)\-\.\+]/', '', $value);

                    // Remove all numeric digits to see if anything else remains
                    $nonNumericChars = preg_replace('/[0-9]/', '', $cleanedValue);

                    // If non-numeric characters remain, it's invalid
                    if (!empty($nonNumericChars)) {
                        $cellErrors[$i] = 'Phone number contains invalid characters. Please use numbers and allowed symbols only.';
                        $isValid = false;
                    }
                    break;

                case 13: // Refills - must be numeric
                    if (!is_numeric($value)) {
                        $cellErrors[$i] = 'Must be numeric';
                        $isValid = false;
                    }
                    break;

                case 14: // Vial Qty - must be numeric
                    if (!is_numeric($value)) {
                        $cellErrors[$i] = 'Must be numeric';
                        $isValid = false;
                    }
                    break;

                // No validation for columns 10, 11, 12 (Medication, Stregnth, Dosing)

                case 15: // SIG - validate if provided
                    // No specific validation rules for SIG content at this time
                    break;

                case 16: // Note - validate if provided
                    // No specific validation rules for Note content at this time
                    break;
            }
        }

        // Any columns beyond index 16 are ignored for validation

        return [$isValid, $cellErrors];
    }

    /**
     * Check if a column header matches the expected header (case-insensitive)
     * This method is kept for backward compatibility but is no longer used
     *
     * @param string $actualHeader The actual header from the Excel file
     * @param string $expectedHeader The expected header
     * @return bool Whether the headers match
     */
    private function matchesColumnHeader($actualHeader, $expectedHeader)
    {
        // Case-insensitive comparison
        return strtolower(trim($actualHeader)) === strtolower(trim($expectedHeader));
    }

    /**
     * Get indices for data fields
     *
     * @param int $unused Unused parameter (kept for backward compatibility)
     * @return array Associative array of field names to indices
     */
    private function getAdjustedIndices($unused = -1)
    {
        // Default indices for each field
        $indices = [
            'script_date' => 0,
            'last_name' => 1,
            'first_name' => 2,
            'dob' => 3,
            'gender' => 4,
            'address' => 5,
            'city' => 6,
            'state' => 7,
            'zip' => 8,
            'phone' => 9,
            'medication' => 10,
            'stregnth' => 11,
            'dosing' => 12,
            'refills' => 13,
            'vial_quantity' => 14,
            'sig' => 15,
            'notes' => 16
        ];

        return $indices;
    }

    /**
     * Convert Excel date value to MySQL date format
     *
     * @param mixed $excelDate The date value from Excel
     * @return string|null The date in MySQL format (Y-m-d) or null if invalid
     */
    private function convertExcelDate($excelDate)
    {
        // If the value is already in a date format like '9/19/1971' (mm/dd/yyyy), parse it
        if (is_string($excelDate) && preg_match('/^\d{1,2}\/\d{1,2}\/\d{4}$/', $excelDate)) {
            try {
                // Parse as mm/dd/yyyy format explicitly
                if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $excelDate, $matches)) {
                    $month = (int)$matches[1];
                    $day = (int)$matches[2];
                    $year = (int)$matches[3];

                    // Validate month and day values
                    if ($month >= 1 && $month <= 12 && $day >= 1 && $day <= 31) {
                        return Carbon::createFromDate($year, $month, $day)->format('Y-m-d');
                    }
                }
            } catch (\Exception $e) {
                // If parsing fails, continue to other methods
                Log::info('Failed to parse mm/dd/yyyy date: ' . $excelDate . ' - ' . $e->getMessage());
            }
        }

        // Check for yyyy/mm/dd or yyyy-mm-dd format
        if (is_string($excelDate) && (preg_match('/^(\d{4})\/(\d{1,2})\/(\d{1,2})$/', $excelDate, $matches) ||
            preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $excelDate, $matches))) {
            try {
                $year = (int)$matches[1];
                $month = (int)$matches[2];
                $day = (int)$matches[3];

                // Validate month and day values
                if ($month >= 1 && $month <= 12 && $day >= 1 && $day <= 31) {
                    return Carbon::createFromDate($year, $month, $day)->format('Y-m-d');
                }
            } catch (\Exception $e) {
                // If parsing fails, continue to other methods
                Log::info('Failed to parse yyyy/mm/dd date: ' . $excelDate . ' - ' . $e->getMessage());
            }
        }

        // If it's a numeric value (Excel date serial number)
        if (is_numeric($excelDate)) {
            try {
                // Convert Excel date to PHP DateTime
                $dateTime = Date::excelToDateTimeObject((float)$excelDate);
                return $dateTime->format('Y-m-d');
            } catch (\Exception $e) {
                // If conversion fails, return null
                Log::info('Failed to parse Excel date number: ' . $excelDate . ' - ' . $e->getMessage());
                return null;
            }
        }

        // If it's already a valid date string in Y-m-d format
        if (is_string($excelDate) && preg_match('/^\d{4}-\d{2}-\d{2}$/', $excelDate)) {
            return $excelDate;
        }

        // Try general parsing as a last resort
        if (is_string($excelDate)) {
            try {
                return Carbon::parse($excelDate)->format('Y-m-d');
            } catch (\Exception $e) {
                // If parsing fails, log and return null
                Log::info('Failed to parse date with general parser: ' . $excelDate . ' - ' . $e->getMessage());
            }
        }

        // For any other format or invalid date, return null
        return null;
    }
    /**
     * Update the status of prescriptions
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request)
    {
        $importId = $request->input('import_id');
        $ids = $request->input('ids');
        $status = $request->input('status');

        // Log the inputs for debugging
        Log::info('Update Status Request', [
            'import_id' => $importId,
            'ids' => $ids,
            'status' => $status
        ]);

        try {
            // Start with a fresh query
            $query = ImportFile::query();

            // First, handle the case where specific IDs are provided
            if ($ids && is_array($ids) && !empty($ids)) {
                Log::info('Processing specific IDs', ['count' => count($ids)]);

                // Convert string IDs to integers if needed
                $numericIds = array_map(function ($id) {
                    return is_numeric($id) ? (int)$id : $id;
                }, $ids);

                // Only update the specific IDs provided
                $query->whereIn('id', $numericIds);

                // If we're updating to "Pending Approval" status, only update records that are currently "New"
                if ($status === ImportFile::STATUS_PENDING_APPROVAL) {
                    $query->where('status', ImportFile::STATUS_NEW);
                    Log::info('Filtering for New status only');
                }
            }
            // If no specific IDs but import_id is provided
            elseif ($importId && is_numeric($importId)) {
                Log::info('Processing all records for import', ['import_id' => $importId]);

                // Filter by import ID
                $query->where('import_id', $importId);

                // If we're updating to "Pending Approval" status, only update records that are currently "New"
                if ($status === ImportFile::STATUS_PENDING_APPROVAL) {
                    $query->where('status', ImportFile::STATUS_NEW);
                    Log::info('Filtering for New status only');
                }
            }
            // If we have no import_id but we have visible IDs from the datatable, try to get the import_id from the first record
            else if ($request->has('visible_ids') && is_array($request->input('visible_ids')) && !empty($request->input('visible_ids'))) {
                $visibleIds = $request->input('visible_ids');
                Log::info('No import_id provided, but visible IDs are available', ['count' => count($visibleIds)]);

                // Get the first visible ID
                $firstId = $visibleIds[0];
                $firstRecord = ImportFile::find($firstId);

                if ($firstRecord && $firstRecord->import_id) {
                    $importId = $firstRecord->import_id;
                    Log::info('Retrieved import_id from first visible record', ['import_id' => $importId]);

                    // Now that we have the import_id, filter by it
                    $query->where('import_id', $importId);

                    // If we're updating to "Pending Approval" status, only update records that are currently "New"
                    if ($status === ImportFile::STATUS_PENDING_APPROVAL) {
                        $query->where('status', ImportFile::STATUS_NEW);
                        Log::info('Filtering for New status only');
                    }
                } else {
                    Log::error('Failed to get import_id from first visible record', ['first_id' => $firstId]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Unable to identify the import ID from the displayed records.'
                    ]);
                }
            }
            // If we have specific IDs directly (not through import_id)
            else if ($request->has('record_ids') && is_array($request->input('record_ids')) && !empty($request->input('record_ids'))) {
                $recordIds = $request->input('record_ids');
                Log::info('Using direct record IDs', ['count' => count($recordIds)]);

                // Convert string IDs to integers if needed
                $numericIds = array_map(function ($id) {
                    return is_numeric($id) ? (int)$id : $id;
                }, $recordIds);

                // Only update the specific IDs provided
                $query->whereIn('id', $numericIds);

                // If we're updating to "Pending Approval" status, only update records that are currently "New"
                if ($status === ImportFile::STATUS_PENDING_APPROVAL) {
                    $query->where('status', ImportFile::STATUS_NEW);
                    Log::info('Filtering for New status only');
                }
            }
            // No valid criteria provided
            else {
                return response()->json([
                    'success' => false,
                    'message' => 'No script specified, or the import ID is invalid.'
                ]);
            }

            // Get the SQL query for debugging
            $sql = $query->toSql();
            $bindings = $query->getBindings();
            Log::info('Update Status Query', [
                'sql' => $sql,
                'bindings' => $bindings
            ]);

            // Get the records that will be updated (for debugging)
            $recordsToUpdate = clone $query;
            $recordIds = $recordsToUpdate->pluck('id')->toArray();
            Log::info('Records that will be updated', [
                'record_ids' => $recordIds,
                'count' => count($recordIds)
            ]);

            // Only if updating to "Sent", process faxes    fax code
            // if ($status === ImportFile::STATUS_PENDING_APPROVAL && !empty($recordIds)) {
            //     $importedFiles = ImportFile::whereIn('id', $recordIds)->get();
            //     $filepathArray = [];

            //     foreach ($importedFiles as $data) {
            //         $file = $data->file_path;
            //         if (Storage::exists($file)) {
            //             $filepathArray[] = Storage::path($file);
            //         }
            //     }

            //     $uploadedFiles = $filepathArray;

            //     if (!is_array($uploadedFiles)) {
            //         $uploadedFiles = [$uploadedFiles];
            //     }

            //     $filesArray = [];
            //     foreach ($uploadedFiles as $file) {
            //         $response = FaxManager::uploadFiles($file);

            //         if (isset($response['path'])) {
            //             $filesArray[] = $response['path'];
            //         }
            //     }

            //     if (!empty($filesArray)) {
            //         $userId = $request->input('user_id', config('fax.user_id'));
            //         $to = $request->input('to', config('fax.to_number'));
            //         $from = $request->input('from', config('fax.from_number'));

            //         $sendFax = FaxManager::sendFax($to, $filesArray, $from, $userId);

            //         if ($sendFax) {
            //             Log::info('Fax sent successfully', [
            //                 'to' => $to,
            //                 'from' => $from,
            //                 'files' => $filesArray
            //             ]);
            //         } else {
            //             Log::error('Failed to send fax', [
            //                 'to' => $to,
            //                 'from' => $from,
            //                 'files' => $filesArray
            //             ]);
            //         }
            //     }
            // }

            // If we're updating to "Pending Approval" status, sign the PDFs
            if ($status === ImportFile::STATUS_PENDING_APPROVAL) {
                // Increase the PHP execution time limit for this request
                set_time_limit(300); // 5 minutes

                // Get the logged-in user
                $user = Auth::user();

                // Log the user information for debugging
                Log::info('User information for signature', [
                    'user_id' => $user ? $user->id : 'No user',
                    'user_name' => $user ? ($user->first_name . ' ' . $user->last_name) : 'No user',
                    'has_signature' => $user && $user->signature ? 'Yes' : 'No',
                    'signature_path' => $user ? $user->signature : 'No user'
                ]);

                // Get the user's signature
                $userSignature = null;
                $doctorName = 'Dr. April'; // Default name

                if ($user) {
                    // Get the user's full name for the signature
                    $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);

                    // Get the user's signature image
                    if ($user->signature) {
                        // For PDF generation, we need to use the absolute file path
                        // instead of a URL because DomPDF might not be able to access external URLs
                        $signaturePath = storage_path('app/public/' . $user->signature);

                        // Check if the file exists
                        if (file_exists($signaturePath)) {
                            try {
                                // Check file size before reading (limit to 2MB to prevent memory issues)
                                $fileSize = filesize($signaturePath);
                                if ($fileSize > 2 * 1024 * 1024) {
                                    throw new \Exception("The signature image file exceeds the maximum allowed size: " . ($fileSize / 1024 / 1024) . " MB");
                                }

                                // Read the file contents with error handling
                                $fileContents = @file_get_contents($signaturePath);

                                if ($fileContents === false) {
                                    throw new \Exception("Unable to read the signature image file. Please try again: " . error_get_last()['message']);
                                }

                                // Validate that the file is actually an image
                                $imageInfo = @getimagesize($signaturePath);
                                if ($imageInfo === false) {
                                    throw new \Exception("Unable to read the signature image file. Please provide a valid image file.");
                                }

                                // Check if the image format is supported by DomPDF
                                $supportedFormats = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF];
                                if (!in_array($imageInfo[2], $supportedFormats)) {
                                    throw new \Exception("Unsupported image format. Please upload a JPG, PNG, or GIF file.");
                                }

                                // Use data URI for the image to ensure it's embedded in the PDF
                                $imageData = base64_encode($fileContents);

                                // Get the MIME type from the image info (more accurate than extension)
                                $mimeType = $imageInfo['mime'];

                                // Fallback to extension-based MIME type if not available
                                if (empty($mimeType)) {
                                    $extension = strtolower(pathinfo($signaturePath, PATHINFO_EXTENSION));
                                    $mimeType = 'image/jpeg'; // Default

                                    // Map common image extensions to MIME types
                                    $mimeTypes = [
                                        'jpg' => 'image/jpeg',
                                        'jpeg' => 'image/jpeg',
                                        'png' => 'image/png',
                                        'gif' => 'image/gif',
                                        'webp' => 'image/webp',
                                        'bmp' => 'image/bmp',
                                    ];

                                    if (isset($mimeTypes[$extension])) {
                                        $mimeType = $mimeTypes[$extension];
                                    }
                                }

                                $userSignature = 'data:' . $mimeType . ';base64,' . $imageData;
                                // Log success only if we get here (no exceptions)
                                Log::info('Signature image loaded successfully', [
                                    'user_id' => $user->id,
                                    'signature_path' => $user->signature,
                                    'mime_type' => $mimeType
                                ]);
                            } catch (\Exception $e) {
                                Log::error('Error reading signature file', [
                                    'user_id' => $user->id,
                                    'signature_path' => $user->signature,
                                    'error' => $e->getMessage()
                                ]);

                                // Create a text-based signature as fallback
                                $userSignature = null; // Reset to null to use the signature line instead
                            }
                        } else {
                            // Log error if file doesn't exist
                            Log::error('Signature image file not found', [
                                'user_id' => $user->id,
                                'signature_path' => $user->signature,
                                'full_path' => $signaturePath
                            ]);
                        }
                    }
                }

                // Check if we have a client timestamp from the request
                $clientTimestamp = $request->input('client_timestamp');

                // If client timestamp is provided, use it for signed_at
                if ($clientTimestamp) {
                    try {
                        // Parse the timestamp with timezone information
                        $signedAt = Carbon::createFromFormat('Y-m-d H:i:s O', $clientTimestamp);
                        // Update status and signed_at with client timestamp
                        $count = $query->update([
                            'status' => $status,
                            'signed_at' => $signedAt
                        ]);
                        Log::info('Records updated with client timestamp', [
                            'count' => $count,
                            'timestamp' => $clientTimestamp
                        ]);
                    } catch (\Exception $e) {
                        // If parsing fails, fall back to session timestamp or current time
                        Log::error('Failed to parse client timestamp', [
                            'timestamp' => $clientTimestamp,
                            'error' => $e->getMessage()
                        ]);
                        $count = $this->updateWithSessionOrCurrentTime($query, $status);
                    }
                } else {
                    // No client timestamp in request, try session or current time
                    $count = $this->updateWithSessionOrCurrentTime($query, $status);
                }

                // Get all the records that were updated
                $filesToUpdate = ImportFile::whereIn('id', $recordIds)->get();

                // Process in smaller batches to avoid memory issues
                $batchSize = 10; // Process 10 files at a time
                $totalFiles = count($filesToUpdate);
                $processedCount = 0;

                Log::info('Starting PDF regeneration for ' . $totalFiles . ' files');

                // Process files in batches
                foreach (array_chunk($filesToUpdate->toArray(), $batchSize) as $batch) {
                    foreach ($batch as $fileData) {
                        $file = (object)$fileData; // Convert array to object for easier access

                        try {
                            // Get the original data for this file
                            $rowData = [
                                $file->script_date ? date('m/d/Y', strtotime($file->script_date)) : '',
                                $file->last_name,
                                $file->first_name,
                                $file->dob ? date('m/d/Y', strtotime($file->dob)) : '',
                                $file->gender,
                                $file->address,
                                $file->city,
                                $file->state,
                                $file->zip,
                                $file->phone ?? '', // Phone is optional
                                $file->medication,
                                $file->stregnth,
                                $file->dosing,
                                $file->refills,
                                $file->vial_quantity,
                                $file->sig ?? '', // SIG field (index 15) is optional
                                $file->notes ?? '' // Notes field (index 16) is optional
                            ];

                            // Get the user's state information if available
                            $userState = null;
                            if ($user && $user->state_id) {
                                $userState = State::find($user->state_id);
                            }

                            // Get the signed_at timestamp from the file record or use current time
                            $signedAtTimestamp = $file->signed_at ? Carbon::parse($file->signed_at) : Carbon::now();

                            // Generate a new PDF with the signature and user information
                            $pdf = PDF::loadView('pdf.prescription', [
                                'data' => $rowData,
                                'isPdfDownload' => true,
                                'userSignature' => $userSignature,
                                'doctorName' => $doctorName,
                                'user' => $user,
                                'userState' => $userState,
                                'signed_at' => $signedAtTimestamp->format('m/d/Y h:i A'),
                                'ip_address' => request()->ip(),
                                'isSigned' => true
                            ]);
                            $pdf->setPaper('letter');

                            // Save the new PDF, overwriting the old one
                            Storage::put($file->file_path, $pdf->output());

                            // Free up memory
                            $pdf = null;
                            unset($pdf);

                            $processedCount++;

                            // Log progress every 5 files
                            if ($processedCount % 5 == 0) {
                                Log::info("Processed $processedCount of $totalFiles PDFs");
                            }
                        } catch (\Exception $e) {
                            Log::error('Error generating PDF for file ID ' . $file->id . ': ' . $e->getMessage());
                            // Continue with the next file even if this one fails
                            continue;
                        }
                    }

                    // Force garbage collection between batches
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                }

                Log::info('Completed PDF regeneration. Processed ' . $processedCount . ' of ' . $totalFiles . ' files');

                // Get the updated count for the response
                $updatedCount = 0;
                if ($importId) {
                    $updatedCount = ImportFile::where('import_id', $importId)
                        ->where('status', $status)
                        ->count();
                }

                // Get the updated files to dispatch the event
                $updatedFiles = [];
                if (!empty($recordIds)) {
                    $updatedFiles = ImportFile::whereIn('id', $recordIds)->get()->toArray();
                } elseif ($importId) {
                    $updatedFiles = ImportFile::where('import_id', $importId)
                        ->where('status', $status)
                        ->get()
                        ->toArray();
                }

                // Dispatch a single ScriptStatusChanged event if there are updated files
                if (!empty($updatedFiles) && $user) {
                    event(new ScriptStatusChanged($updatedFiles, $user));
                    Log::info('ScriptStatusChanged event dispatched', [
                        'user_id' => $user->id,
                        'user_name' => $user->first_name . ' ' . $user->last_name,
                        'count' => count($updatedFiles)
                    ]);
                }

                // We've already updated the status above, so we don't need to do it again
                return response()->json([
                    'success' => true,
                    'message' => $count . ' prescriptions updated to ' . $status . '. ' . $processedCount . ' PDFs regenerated.',
                    'redirect' => route('excel.view-sent', ['importId' => $importId]),
                    'count' => $updatedCount
                ]);
            }

            // Update status
            $count = $query->update(['status' => $status]);
            Log::info('Records updated', ['count' => $count]);

            // Determine redirect URL based on status
            $redirectUrl = route('imports.index'); // Default fallback

            // If we have an import ID, use it for redirection
            if ($importId && is_numeric($importId)) {
                $redirectUrl = route('excel.view', ['id' => $importId]);
                if ($status === ImportFile::STATUS_PENDING_APPROVAL) {
                    $redirectUrl = route('excel.view-sent', ['importId' => $importId]);
                }
            }
            // If we don't have an import ID but we have specific IDs, get the import ID from the first record
            elseif ($ids && is_array($ids) && !empty($ids)) {
                $firstRecord = ImportFile::find($ids[0]);
                if ($firstRecord && $firstRecord->import_id) {
                    $importId = $firstRecord->import_id;
                    $redirectUrl = route('excel.view', ['id' => $importId]);
                    if ($status === ImportFile::STATUS_PENDING_APPROVAL) {
                        $redirectUrl = route('excel.view-sent', ['importId' => $importId]);
                    }
                }
            }

            // Get the updated count for the response
            $updatedCount = 0;
            if ($importId) {
                $updatedCount = ImportFile::where('import_id', $importId)
                    ->where('status', $status)
                    ->count();
            }

            // If status is changed to PENDING_APPROVAL, dispatch the event
            if ($status === ImportFile::STATUS_PENDING_APPROVAL) {
                // Get the updated files to dispatch the event
                $updatedFiles = [];
                if (!empty($ids)) {
                    $updatedFiles = ImportFile::whereIn('id', $ids)->get()->toArray();
                } elseif ($importId) {
                    $updatedFiles = ImportFile::where('import_id', $importId)
                        ->where('status', $status)
                        ->get()
                        ->toArray();
                }

                // Get the logged-in user
                $user = Auth::user();

                // Dispatch a single ScriptStatusChanged event if there are updated files
                if (!empty($updatedFiles) && $user) {
                    event(new ScriptStatusChanged($updatedFiles, $user));
                    Log::info('ScriptStatusChanged event dispatched', [
                        'user_id' => $user->id,
                        'user_name' => $user->first_name . ' ' . $user->last_name,
                        'count' => count($updatedFiles)
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => $count . ' prescriptions updated to ' . $status,
                'redirect' => $redirectUrl,
                'count' => $updatedCount
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating status: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Download selected PDFs as a ZIP file
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    /**
     * Get the import_id for a specific record
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getImportId(Request $request)
    {
        $id = $request->input('id');

        if (!$id) {
            return response()->json([
                'success' => false,
                'message' => 'No record ID provided'
            ]);
        }

        try {
            $importFile = ImportFile::find($id);

            if (!$importFile) {
                return response()->json([
                    'success' => false,
                    'message' => 'Record not found'
                ]);
            }

            return response()->json([
                'success' => true,
                'import_id' => $importFile->import_id
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting import_id', [
                'record_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving import ID: ' . $e->getMessage()
            ]);
        }
    }

    public function downloadSelectedPdf(Request $request)
    {
        $ids = $request->input('ids');

        if (!$ids || !is_array($ids) || empty($ids)) {
            return back()->with('error', 'No prescriptions selected');
        }

        // Get the ImportFile records
        $importFiles = ImportFile::whereIn('id', $ids)->get();

        if ($importFiles->isEmpty()) {
            return back()->with('error', 'No prescription files found');
        }

        // Create a temporary directory to store PDFs
        $tempDir = storage_path('app/temp/prescriptions_' . time());
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        // Copy each file to the temp directory
        foreach ($importFiles as $importFile) {
            if (Storage::exists($importFile->file_path)) {
                $fileContent = Storage::get($importFile->file_path);

                // Create a more descriptive filename based on patient data
                // Include the number field to make the filename more meaningful to the user
                $fileName = 'prescription_' .
                    ($importFile->number ? 'row' . $importFile->number . '_' : '') .
                    $importFile->id . '_' .
                    $importFile->last_name . '_' .
                    $importFile->first_name . '.pdf';

                // Replace spaces with underscores and remove any special characters
                $fileName = str_replace(' ', '_', $fileName);
                $fileName = preg_replace('/[^A-Za-z0-9\._-]/', '', $fileName);

                file_put_contents($tempDir . '/' . $fileName, $fileContent);
            }
        }

        // Create a ZIP file
        $zipFileName = 'selected_prescriptions_' . date('Y-m-d') . '.zip';
        $zipFilePath = storage_path('app/temp/' . $zipFileName);

        $zip = new \ZipArchive();
        if ($zip->open($zipFilePath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === TRUE) {
            // Add files to the zip
            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($tempDir),
                \RecursiveIteratorIterator::LEAVES_ONLY
            );

            foreach ($files as $file) {
                if (!$file->isDir()) {
                    $filePath = $file->getRealPath();
                    // Use a more unique naming scheme to prevent filename collisions
                    // Include the full path hash to ensure uniqueness
                    $pathHash = substr(md5($filePath), 0, 8);
                    $relativePath = $pathHash . '_' . basename($filePath);
                    $zip->addFile($filePath, $relativePath);
                }
            }

            $zip->close();

            // Clean up the temporary directory
            array_map('unlink', glob("$tempDir/*.*"));
            rmdir($tempDir);

            // Return the ZIP file for download
            return response()->download($zipFilePath, $zipFileName, [
                'Content-Type' => 'application/zip'
            ])->deleteFileAfterSend(true);
        }

        return back()->with('error', 'Unable to create ZIP file. Please try again later.');
    }

    /**
     * Store the client device time in the session
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeDeviceTime(Request $request)
    {
        $deviceTime = $request->input('device_time');

        if (!$deviceTime) {
            return response()->json([
                'success' => false,
                'message' => 'Device time not provided'
            ]);
        }

        // Store the device time in the session
        session(['device_time' => $deviceTime]);

        // Log the device time for debugging
        Log::info('Device time stored in session', [
            'device_time' => $deviceTime,
            'user_id' => auth()->id()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Device time stored successfully'
        ]);
    }

    /**
     * Helper method to update records with device time from session or current server time
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $status
     * @return int Number of records updated
     */
    private function updateWithSessionOrCurrentTime($query, $status)
    {
        // Try to get device time from session
        $deviceTime = session('device_time');

        if ($deviceTime) {
            try {
                // Parse the timestamp with timezone information
                $signedAt = Carbon::createFromFormat('Y-m-d H:i:s O', $deviceTime);
                $count = $query->update([
                    'status' => $status,
                    'signed_at' => $signedAt
                ]);
                Log::info('Using device time from session for signed_at', [
                    'timestamp' => $deviceTime
                ]);
                return $count;
            } catch (\Exception $e) {
                Log::error('Failed to parse device time from session', [
                    'timestamp' => $deviceTime,
                    'error' => $e->getMessage()
                ]);
                // Fall through to use current time
            }
        }

        // If no device time in session or parsing failed, use current server time
        $count = $query->update([
            'status' => $status,
            'signed_at' => Carbon::now()
        ]);
        Log::info('Using current server time for signed_at');
        return $count;
    }
}
