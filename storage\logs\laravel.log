[2025-05-30 09:38:37] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"JltC4UNJVZCX0D2wr6kk3brcfphElpYtyn4EYlec","status":"Pending Approval","changed_status":"Sent","displayed_ids":["657"]},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":["657"]} 
[2025-05-30 09:38:37] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-05-30 09:38:37] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-05-30 09:38:37] local.INFO: Filtering displayed_ids {"original_count":1,"filtered_count":1,"original_ids":["657"],"filtered_ids":["657"]} 
[2025-05-30 09:38:38] local.INFO: Using filtered displayed_ids {"count":1,"results_count":1} 
[2025-05-30 09:38:38] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_ids":[657],"count":1} 
[2025-05-30 09:38:38] local.INFO: SendFilesToFaxJob dispatched successfully  
[2025-05-30 09:38:40] local.INFO: SendFilesToFaxJob starting {"import_file_ids":[657],"count":1} 
[2025-05-30 09:38:40] local.INFO: Files retrieved for processing {"count":1,"file_ids":[657]} 
[2025-05-30 09:38:40] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 09:38:40] local.INFO: Merging multiple PDFs {"file_count":1} 
[2025-05-30 09:38:44] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:08:43 +0630","user_id":1} 
[2025-05-30 09:38:45] local.INFO: Successfully merged and uploaded PDFs {"merged_count":1,"merged_file":"temp/fax_merged_pdfs/upSX5u4sDOq96stjuZET.pdf"} 
[2025-05-30 09:38:46] local.INFO: Fax numbers retrieved with + prefix {"count":1,"numbers":["+18885110528"]} 
[2025-05-30 09:38:47] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 09:38:47] local.INFO: Fax sent successfully {"to":["+18885110528"],"from":"+18669938841","files":["/storage/transient-6921bd4990ca4eee8da3c399e7ce7f9e.pdf"]} 
[2025-05-30 09:38:47] local.INFO: Cleaned up temporary PDF file {"file":"temp/fax_merged_pdfs/upSX5u4sDOq96stjuZET.pdf"} 
[2025-05-30 09:38:47] local.INFO: Removed empty temporary directory {"directory":"temp/fax_merged_pdfs"} 
[2025-05-30 09:57:06] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:27:05 +0630","user_id":1} 
[2025-05-30 10:00:37] local.INFO: sendFaxAll request parameters {"all_params":{"_token":"JltC4UNJVZCX0D2wr6kk3brcfphElpYtyn4EYlec","status":"Pending Approval","changed_status":"Sent"},"provider_id":null,"has_provider_id":false,"medication_id":null,"signed_date":null,"displayed_ids":null} 
[2025-05-30 10:00:37] local.WARNING: No provider filter applied {"has_provider_id":false,"provider_id_value":null} 
[2025-05-30 10:00:37] local.INFO: Final query before execution {"sql":"select * from `import_files` where `status` = ?","bindings":["Pending Approval"]} 
[2025-05-30 10:00:37] local.INFO: No displayed_ids provided, using all matching records {"results_count":6} 
[2025-05-30 10:00:37] local.INFO: Sending IDs to SendFilesToFaxJob {"import_file_ids":[658,659,660,661,662,663],"count":6} 
[2025-05-30 10:00:37] local.INFO: SendFilesToFaxJob dispatched successfully  
[2025-05-30 10:00:40] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:30:39 +0630","user_id":1} 
[2025-05-30 10:00:41] local.INFO: SendFilesToFaxJob starting {"import_file_ids":[658,659,660,661,662,663],"count":6} 
[2025-05-30 10:00:41] local.INFO: Files retrieved for processing {"count":6,"file_ids":[658,659,660,661,662,663]} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Updated status to Pending Dispatch {"status":"Pending Dispatch"} 
[2025-05-30 10:00:41] local.INFO: Merging multiple PDFs {"file_count":6} 
[2025-05-30 10:00:45] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:30:45 +0630","user_id":1} 
[2025-05-30 10:00:49] local.INFO: Device time stored in session {"device_time":"2025-05-30 15:30:48 +0630","user_id":1} 
[2025-05-30 10:01:05] local.INFO: Successfully merged and uploaded PDFs {"merged_count":6,"merged_file":"temp/fax_merged_pdfs/uTqRilI7iJcKF2xcYY2O.pdf"} 
[2025-05-30 10:01:05] local.INFO: Fax numbers retrieved with + prefix {"count":1,"numbers":["+18885110528"]} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Updated status to sent {"status":"Sent"} 
[2025-05-30 10:01:06] local.INFO: Fax sent successfully {"to":["+18885110528"],"from":"+18669938841","files":["/storage/transient-5fd192d7fbd44ab883cb06af10430db8.pdf"]} 
[2025-05-30 10:01:06] local.INFO: Cleaned up temporary PDF file {"file":"temp/fax_merged_pdfs/uTqRilI7iJcKF2xcYY2O.pdf"} 
[2025-05-30 10:01:06] local.INFO: Removed empty temporary directory {"directory":"temp/fax_merged_pdfs"} 
[2025-06-02 04:44:06] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:14:04 +0630","user_id":1} 
[2025-06-02 04:48:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:18:08 +0630","user_id":1} 
[2025-06-02 04:49:33] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:19:33 +0630","user_id":1} 
[2025-06-02 04:49:59] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:19:59 +0630","user_id":1} 
[2025-06-02 04:50:20] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:20:19 +0630","user_id":1} 
[2025-06-02 04:50:33] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:20:32 +0630","user_id":1} 
[2025-06-02 04:53:27] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:23:27 +0630","user_id":1} 
[2025-06-02 04:53:38] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:23:37 +0630","user_id":1} 
[2025-06-02 04:53:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:23:47 +0630","user_id":1} 
[2025-06-02 04:57:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:27:07 +0630","user_id":1} 
[2025-06-02 04:57:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:27:39 +0630","user_id":1} 
[2025-06-02 04:57:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:27:50 +0630","user_id":1} 
[2025-06-02 04:57:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:27:56 +0630","user_id":1} 
[2025-06-02 04:58:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:28:08 +0630","user_id":1} 
[2025-06-02 04:58:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:28:19 +0630","user_id":1} 
[2025-06-02 04:58:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:28:37 +0630","user_id":1} 
[2025-06-02 04:58:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:28:52 +0630","user_id":1} 
[2025-06-02 04:59:07] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:06 +0630","user_id":1} 
[2025-06-02 04:59:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:10 +0630","user_id":1} 
[2025-06-02 04:59:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:24 +0630","user_id":1} 
[2025-06-02 04:59:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:51 +0630","user_id":1} 
[2025-06-02 05:00:00] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:29:59 +0630","user_id":1} 
[2025-06-02 05:00:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:30:07 +0630","user_id":1} 
[2025-06-02 05:00:41] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:30:40 +0630","user_id":1} 
[2025-06-02 05:01:01] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:00 +0630","user_id":1} 
[2025-06-02 05:01:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:18 +0630","user_id":1} 
[2025-06-02 05:01:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:20 +0630","user_id":1} 
[2025-06-02 05:01:42] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:42 +0630","user_id":1} 
[2025-06-02 05:01:50] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:49 +0630","user_id":1} 
[2025-06-02 05:01:56] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:31:55 +0630","user_id":1} 
[2025-06-02 05:02:27] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:32:26 +0630","user_id":1} 
[2025-06-02 05:03:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:33:02 +0630","user_id":1} 
[2025-06-02 05:03:41] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:33:41 +0630","user_id":1} 
[2025-06-02 05:03:49] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:33:49 +0630","user_id":1} 
[2025-06-02 05:07:57] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:37:57 +0630","user_id":1} 
[2025-06-02 05:08:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:38:36 +0630","user_id":1} 
[2025-06-02 05:08:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:38:52 +0630","user_id":1} 
[2025-06-02 05:09:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:39:07 +0630","user_id":1} 
[2025-06-02 05:09:34] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:39:33 +0630","user_id":1} 
[2025-06-02 05:10:02] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:40:01 +0630","user_id":1} 
[2025-06-02 05:10:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:40:15 +0630","user_id":1} 
[2025-06-02 05:15:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:45:32 +0630","user_id":1} 
[2025-06-02 05:16:21] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:46:21 +0630","user_id":1} 
[2025-06-02 05:16:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:46:23 +0630","user_id":1} 
[2025-06-02 05:20:46] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:50:45 +0630","user_id":1} 
[2025-06-02 05:21:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:51:03 +0630","user_id":1} 
[2025-06-02 05:21:26] local.INFO: Device time stored in session {"device_time":"2025-06-02 10:51:25 +0630","user_id":1} 
[2025-06-02 05:38:45] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:08:44 +0630","user_id":1} 
[2025-06-02 05:39:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:09:08 +0630","user_id":1} 
[2025-06-02 05:39:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:09:30 +0630","user_id":1} 
[2025-06-02 05:39:49] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:09:48 +0630","user_id":1} 
[2025-06-02 05:40:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:10:35 +0630","user_id":1} 
[2025-06-02 05:43:34] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:13:34 +0630","user_id":1} 
[2025-06-02 05:45:29] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:15:29 +0630","user_id":1} 
[2025-06-02 05:45:44] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:15:44 +0630","user_id":1} 
[2025-06-02 05:47:23] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:17:23 +0630","user_id":1} 
[2025-06-02 05:48:10] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:18:10 +0630","user_id":1} 
[2025-06-02 05:48:39] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:18:38 +0630","user_id":1} 
[2025-06-02 05:49:11] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:19:11 +0630","user_id":1} 
[2025-06-02 05:49:20] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:19:20 +0630","user_id":1} 
[2025-06-02 05:50:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:20:19 +0630","user_id":1} 
[2025-06-02 05:50:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:20:30 +0630","user_id":1} 
[2025-06-02 05:52:14] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:22:13 +0630","user_id":1} 
[2025-06-02 05:54:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:24:51 +0630","user_id":1} 
[2025-06-02 05:55:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:25:14 +0630","user_id":1} 
[2025-06-02 05:55:37] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:25:37 +0630","user_id":1} 
[2025-06-02 05:58:18] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:28:17 +0630","user_id":1} 
[2025-06-02 06:05:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:35:16 +0630","user_id":1} 
[2025-06-02 06:08:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:38:04 +0630","user_id":1} 
[2025-06-02 06:08:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:38:23 +0630","user_id":1} 
[2025-06-02 06:09:14] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:39:13 +0630","user_id":1} 
[2025-06-02 06:10:24] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:40:23 +0630","user_id":1} 
[2025-06-02 06:10:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:40:51 +0630","user_id":1} 
[2025-06-02 06:11:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:41:43 +0630","user_id":1} 
[2025-06-02 06:12:13] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:42:13 +0630","user_id":1} 
[2025-06-02 06:12:32] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:42:32 +0630","user_id":1} 
[2025-06-02 06:12:42] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:42:42 +0630","user_id":1} 
[2025-06-02 06:13:15] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:43:14 +0630","user_id":1} 
[2025-06-02 06:13:51] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:43:50 +0630","user_id":1} 
[2025-06-02 06:14:13] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:44:12 +0630","user_id":1} 
[2025-06-02 06:14:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:44:30 +0630","user_id":1} 
[2025-06-02 06:15:58] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:45:57 +0630","user_id":1} 
[2025-06-02 06:16:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:46:34 +0630","user_id":1} 
[2025-06-02 06:17:23] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:47:22 +0630","user_id":1} 
[2025-06-02 06:18:31] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:48:30 +0630","user_id":1} 
[2025-06-02 06:19:04] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:49:04 +0630","user_id":1} 
[2025-06-02 06:19:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:49:42 +0630","user_id":1} 
[2025-06-02 06:20:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:50:09 +0630","user_id":1} 
[2025-06-02 06:20:24] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:181)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(181): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(107): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:22:00] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:52:00 +0630","user_id":1} 
[2025-06-02 06:22:04] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:183)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(183): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:29:38] local.INFO: Device time stored in session {"device_time":"2025-06-02 11:59:37 +0630","user_id":1} 
[2025-06-02 06:30:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:00:29 +0630","user_id":1} 
[2025-06-02 06:31:08] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:01:08 +0630","user_id":1} 
[2025-06-02 06:31:13] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:183)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(183): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:31:57] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:01:56 +0630","user_id":1} 
[2025-06-02 06:32:02] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:183)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(183): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:32:09] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:02:08 +0630","user_id":1} 
[2025-06-02 06:32:12] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:02:12 +0630","user_id":1} 
[2025-06-02 06:32:21] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:183)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(183): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:33:46] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:03:45 +0630","user_id":1} 
[2025-06-02 06:33:50] local.ERROR: trim(): Argument #1 ($string) must be of type string, array given {"userId":1,"exception":"[object] (TypeError(code: 0): trim(): Argument #1 ($string) must be of type string, array given at C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php:184)
[stacktrace]
#0 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(184): trim(Array)
#1 C:\\KodeCreators\\newlife-panel\\app\\Http\\Livewire\\Settings\\FaxOptions.php(109): App\\Http\\Livewire\\Settings\\FaxOptions->validateFaxNumbers()
#2 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Livewire\\Settings\\FaxOptions->store()
#3 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#7 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php(36): Livewire\\Component->callMethod('store', Array, Object(Closure))
#8 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate(Object(App\\Http\\Livewire\\Settings\\FaxOptions), Object(Livewire\\Request))
#9 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#10 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php(18): Livewire\\Connection\\ConnectionHandler->handle(Array)
#11 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke('settings.fax-op...')
#12 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Controllers\\HttpConnectionHandler), '__invoke')
#13 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\KodeCreators\\newlife-panel\\app\\Http\\Middleware\\CheckUserStatus.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckUserStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\KodeCreators\\newlife-panel\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\KodeCreators\\newlife-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\KodeCreators\\newlife-panel\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\KodeCreators\\newlife-panel\\server.php(21): require_once('C:\\\\KodeCreators...')
#57 {main}
"} 
[2025-06-02 06:34:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:04:02 +0630","user_id":1} 
[2025-06-02 06:34:41] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:04:41 +0630","user_id":1} 
[2025-06-02 06:35:38] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:05:37 +0630","user_id":1} 
[2025-06-02 06:36:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:06:27 +0630","user_id":1} 
[2025-06-02 06:37:36] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:07:36 +0630","user_id":1} 
[2025-06-02 06:38:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:08:42 +0630","user_id":1} 
[2025-06-02 06:39:18] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:09:17 +0630","user_id":1} 
[2025-06-02 06:39:45] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:09:44 +0630","user_id":1} 
[2025-06-02 06:40:43] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:10:43 +0630","user_id":1} 
[2025-06-02 06:46:46] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:16:45 +0630","user_id":1} 
[2025-06-02 06:46:53] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"1234567890","label":"Test Fax"}],"newFaxInputs":[{"number":"","label":""}]} 
[2025-06-02 06:47:47] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:17:46 +0630","user_id":1} 
[2025-06-02 06:49:28] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:19:27 +0630","user_id":1} 
[2025-06-02 06:49:46] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"1234567890","label":"Test Fax"}],"newFaxInputs":[{"number":"43165468768","label":"new fax"}]} 
[2025-06-02 06:52:01] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:22:00 +0630","user_id":1} 
[2025-06-02 06:53:00] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:22:59 +0630","user_id":1} 
[2025-06-02 06:53:22] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"12345678905","label":"Test Fax"}],"newFaxInputs":[{"number":"14785125631","label":"new test"}]} 
[2025-06-02 06:53:40] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"12345678905","label":"Test Fax"}],"newFaxInputs":[{"number":"14785125631","label":"new test"}]} 
[2025-06-02 06:58:16] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:28:15 +0630","user_id":1} 
[2025-06-02 06:58:41] local.INFO: FaxOptions store method called {"faxNumbers":[{"id":16,"numbers":"18885110528","label":"test"},{"id":24,"numbers":"1234567890","label":"Test Fax"}],"newFaxInputs":[{"number":"125412563225","label":"new fax"}]} 
[2025-06-02 06:59:30] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:29:30 +0630","user_id":1} 
[2025-06-02 07:00:52] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:30:52 +0630","user_id":1} 
[2025-06-02 07:01:03] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:31:02 +0630","user_id":1} 
[2025-06-02 07:01:41] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:31:41 +0630","user_id":1} 
[2025-06-02 07:02:19] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:32:18 +0630","user_id":1} 
[2025-06-02 07:02:35] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:32:34 +0630","user_id":1} 
[2025-06-02 07:15:53] local.INFO: Device time stored in session {"device_time":"2025-06-02 12:45:53 +0630","user_id":1} 
